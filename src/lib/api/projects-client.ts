import { createClient as createSupabaseClient } from '@/lib/supabase/client'
import { CreateProjectData, UpdateProjectData } from '@/lib/types'

export async function getProjects() {
  const supabase = createSupabaseClient()

  const { data: projects, error } = await supabase
    .from('projects')
    .select(`
      *,
      client:clients(id, name, company),
      created_by_user:users_profiles!created_by(id, full_name)
    `)
    .order('created_at', { ascending: false })

  if (error) {
    throw new Error(`Failed to fetch projects: ${error.message}`)
  }

  // For the projects list, we don't need to fetch full team member details
  // to keep the query performant. Team member details are fetched in getProject()
  return projects.map(project => ({
    ...project,
    assigned_team_members: [] // Will be populated in individual project view
  }))
}

export async function getProject(id: string) {
  const supabase = createSupabaseClient()

  const { data: project, error } = await supabase
    .from('projects')
    .select(`
      *,
      client:clients(id, name, company, email, phone),
      created_by_user:users_profiles!created_by(id, full_name)
    `)
    .eq('id', id)
    .single()

  if (error) {
    throw new Error(`Failed to fetch project: ${error.message}`)
  }

  // Fetch assigned team members separately if they exist
  let assigned_team_members: Array<{ id: string; full_name: string; role: string }> = []
  if (project.assigned_team && Array.isArray(project.assigned_team)) {
    const { data: teamMembers, error: teamError } = await supabase
      .from('users_profiles')
      .select('id, full_name, role')
      .in('id', project.assigned_team)

    if (!teamError && teamMembers) {
      assigned_team_members = teamMembers
    }
  }

  return {
    ...project,
    assigned_team_members
  }
}

export async function createProject(projectData: CreateProjectData) {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('projects')
    .insert(projectData)
    .select()
    .single()
  
  if (error) {
    throw new Error(`Failed to create project: ${error.message}`)
  }
  
  return data
}

export async function updateProject(id: string, projectData: UpdateProjectData) {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('projects')
    .update(projectData)
    .eq('id', id)
    .select()
    .single()
  
  if (error) {
    throw new Error(`Failed to update project: ${error.message}`)
  }
  
  return data
}

export async function deleteProject(id: string) {
  const supabase = createSupabaseClient()
  
  const { error } = await supabase
    .from('projects')
    .delete()
    .eq('id', id)
  
  if (error) {
    throw new Error(`Failed to delete project: ${error.message}`)
  }
}

export async function searchProjects(query: string) {
  const supabase = createSupabaseClient()

  // Prepare search promises for different field types
  const searchPromises = [
    // Search project table fields (name, description)
    supabase
      .from('projects')
      .select(`
        *,
        client:clients(id, name, company),
        created_by_user:users_profiles!created_by(id, full_name),
        assigned_team_members:users_profiles(id, full_name)
      `)
      .or(`name.ilike.%${query}%,description.ilike.%${query}%`),

    // Search by client name
    supabase
      .from('projects')
      .select(`
        *,
        client:clients(id, name, company),
        created_by_user:users_profiles!created_by(id, full_name),
        assigned_team_members:users_profiles(id, full_name)
      `)
      .filter('client.name', 'ilike', `%${query}%`),

    // Search by client company
    supabase
      .from('projects')
      .select(`
        *,
        client:clients(id, name, company),
        created_by_user:users_profiles!created_by(id, full_name),
        assigned_team_members:users_profiles(id, full_name)
      `)
      .filter('client.company', 'ilike', `%${query}%`)
  ]

  // If query is numeric, also search budget
  const numericQuery = parseFloat(query.replace(/[^\d.-]/g, ''))
  if (!isNaN(numericQuery)) {
    searchPromises.push(
      supabase
        .from('projects')
        .select(`
          *,
          client:clients(id, name, company),
          created_by_user:users_profiles!created_by(id, full_name),
          assigned_team_members:users_profiles(id, full_name)
        `)
        .filter('budget', 'eq', numericQuery)
    )
  }

  try {
    // Execute all search queries in parallel
    const results = await Promise.all(searchPromises)

    // Check for errors in any of the queries
    for (const result of results) {
      if (result.error) {
        throw new Error(`Failed to search projects: ${result.error.message}`)
      }
    }

    // Merge and deduplicate results
    const allProjects = results.flatMap(result => result.data || [])
    const uniqueProjects = allProjects.filter((project, index, self) =>
      index === self.findIndex(p => p.id === project.id)
    )

    // Sort by creation date (newest first)
    return uniqueProjects.sort((a, b) =>
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    )
  } catch (error) {
    throw new Error(`Failed to search projects: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export async function getProjectsByStatus(status: string) {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('projects')
    .select(`
      *,
      client:clients(id, name, company),
      created_by_user:users_profiles!created_by(id, full_name),
      assigned_team_members:users_profiles(id, full_name)
    `)
    .eq('status', status)
    .order('created_at', { ascending: false })
  
  if (error) {
    throw new Error(`Failed to fetch projects by status: ${error.message}`)
  }
  
  return data
}

export async function getProjectsByClient(clientId: string) {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('projects')
    .select(`
      *,
      client:clients(id, name, company),
      created_by_user:users_profiles!created_by(id, full_name),
      assigned_team_members:users_profiles(id, full_name)
    `)
    .eq('client_id', clientId)
    .order('created_at', { ascending: false })
  
  if (error) {
    throw new Error(`Failed to fetch projects by client: ${error.message}`)
  }
  
  return data
}

export async function getAvailableClients() {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('clients')
    .select('id, name, company')
    .in('status', ['active', 'lead'])
    .order('name', { ascending: true })
  
  if (error) {
    throw new Error(`Failed to fetch available clients: ${error.message}`)
  }
  
  return data
}

export async function getAvailableUsers() {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('users_profiles')
    .select('id, full_name, role')
    .order('full_name', { ascending: true })
  
  if (error) {
    throw new Error(`Failed to fetch available users: ${error.message}`)
  }
  
  return data
}
