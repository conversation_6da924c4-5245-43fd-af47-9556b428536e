import { createClient as createSupabaseClient } from '@/lib/supabase/client'
import { CreateInvoiceData, UpdateInvoiceData } from '@/lib/types'

export async function getInvoices() {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('invoices')
    .select(`
      *,
      client:clients(id, name, company),
      project:projects(id, name),
      created_by_user:users_profiles!created_by(id, full_name)
    `)
    .order('created_at', { ascending: false })
  
  if (error) {
    throw new Error(`Failed to fetch invoices: ${error.message}`)
  }
  
  return data
}

export async function getInvoice(id: string) {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('invoices')
    .select(`
      *,
      client:clients(id, name, company, email, phone, address),
      project:projects(id, name, description),
      created_by_user:users_profiles!created_by(id, full_name)
    `)
    .eq('id', id)
    .single()
  
  if (error) {
    throw new Error(`Failed to fetch invoice: ${error.message}`)
  }
  
  return data
}

export async function createInvoice(invoiceData: CreateInvoiceData) {
  const supabase = createSupabaseClient()

  // Generate invoice number
  const invoiceNumber = await generateInvoiceNumber()

  const { data, error } = await supabase
    .from('invoices')
    .insert({
      ...invoiceData,
      invoice_number: invoiceNumber,
    })
    .select()
    .single()

  if (error) {
    throw new Error(`Failed to create invoice: ${error.message}`)
  }

  return data
}

export async function updateInvoice(id: string, invoiceData: UpdateInvoiceData) {
  const supabase = createSupabaseClient()

  const { data, error } = await supabase
    .from('invoices')
    .update(invoiceData)
    .eq('id', id)
    .select()
    .single()

  if (error) {
    throw new Error(`Failed to update invoice: ${error.message}`)
  }

  return data
}

// Payment milestone functions
export async function getProjectMilestones(projectId: string) {
  const supabase = createSupabaseClient()

  const { data, error } = await supabase
    .from('invoices')
    .select(`
      id,
      invoice_number,
      amount,
      total_amount,
      status,
      milestone_type,
      milestone_percentage,
      sequence_number,
      due_date,
      paid_date,
      created_at
    `)
    .eq('project_id', projectId)
    .neq('milestone_type', 'standard')
    .order('sequence_number', { ascending: true })

  if (error) {
    throw new Error(`Failed to fetch project milestones: ${error.message}`)
  }

  return data
}

export async function createMilestoneInvoice(milestoneData: {
  project_id: string
  client_id: string
  milestone_type: 'dp' | 'progress' | 'final'
  milestone_percentage: number
  sequence_number: number
  amount: number
  description: string
  due_date?: string
  created_by: string
}) {
  const supabase = createSupabaseClient()

  // Generate invoice number
  const invoiceNumber = await generateInvoiceNumber()

  const { data, error } = await supabase
    .from('invoices')
    .insert({
      invoice_number: invoiceNumber,
      client_id: milestoneData.client_id,
      project_id: milestoneData.project_id,
      amount: milestoneData.amount,
      tax_amount: 0,
      total_amount: milestoneData.amount,
      currency: 'IDR',
      status: 'draft',
      milestone_type: milestoneData.milestone_type,
      milestone_percentage: milestoneData.milestone_percentage,
      sequence_number: milestoneData.sequence_number,
      due_date: milestoneData.due_date || null,
      items: [{
        id: '1',
        description: milestoneData.description,
        quantity: 1,
        rate: milestoneData.amount,
        amount: milestoneData.amount
      }],
      created_by: milestoneData.created_by,
    })
    .select()
    .single()

  if (error) {
    throw new Error(`Failed to create milestone invoice: ${error.message}`)
  }

  return data
}

export async function validateProjectBudget(projectId: string, additionalAmount: number = 0) {
  const supabase = createSupabaseClient()

  // Get project budget
  const { data: project, error: projectError } = await supabase
    .from('projects')
    .select('budget')
    .eq('id', projectId)
    .single()

  if (projectError) {
    throw new Error(`Failed to fetch project: ${projectError.message}`)
  }

  if (!project.budget) {
    return { isValid: true, totalInvoiced: 0, remainingBudget: 0, projectBudget: 0 }
  }

  // Get total invoiced amount for this project
  const { data: invoices, error: invoicesError } = await supabase
    .from('invoices')
    .select('total_amount')
    .eq('project_id', projectId)
    .neq('status', 'cancelled')

  if (invoicesError) {
    throw new Error(`Failed to fetch project invoices: ${invoicesError.message}`)
  }

  const totalInvoiced = invoices.reduce((sum, invoice) => sum + invoice.total_amount, 0)
  const remainingBudget = project.budget - totalInvoiced
  const wouldExceedBudget = (totalInvoiced + additionalAmount) > project.budget

  return {
    isValid: !wouldExceedBudget,
    totalInvoiced,
    remainingBudget,
    projectBudget: project.budget,
    wouldExceedBy: wouldExceedBudget ? (totalInvoiced + additionalAmount) - project.budget : 0
  }
}

export async function deleteInvoice(id: string) {
  const supabase = createSupabaseClient()
  
  const { error } = await supabase
    .from('invoices')
    .delete()
    .eq('id', id)
  
  if (error) {
    throw new Error(`Failed to delete invoice: ${error.message}`)
  }
}

export async function updateInvoiceStatus(id: string, status: string, paidDate?: string) {
  const supabase = createSupabaseClient()
  
  const updateData: { status: string; paid_date?: string | null } = { status }
  if (status === 'paid' && paidDate) {
    updateData.paid_date = paidDate
  }
  
  const { data, error } = await supabase
    .from('invoices')
    .update(updateData)
    .eq('id', id)
    .select()
    .single()
  
  if (error) {
    throw new Error(`Failed to update invoice status: ${error.message}`)
  }
  
  return data
}

// Generate unique invoice number
async function generateInvoiceNumber(): Promise<string> {
  const supabase = createSupabaseClient()

  // Get current year and month
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const prefix = `INV-${year}${month}-`

  // Get all existing invoice numbers for this month to find the highest sequence number
  const { data: existingInvoices, error } = await supabase
    .from('invoices')
    .select('invoice_number')
    .like('invoice_number', `${prefix}%`)
    .order('invoice_number', { ascending: false })
    .limit(1)

  if (error) {
    throw new Error(`Failed to generate invoice number: ${error.message}`)
  }

  let nextSequence = 1

  if (existingInvoices && existingInvoices.length > 0) {
    // Extract the sequence number from the highest invoice number
    const lastInvoiceNumber = existingInvoices[0].invoice_number
    const sequencePart = lastInvoiceNumber.replace(prefix, '')
    const lastSequence = parseInt(sequencePart, 10)

    if (!isNaN(lastSequence)) {
      nextSequence = lastSequence + 1
    }
  }

  const invoiceNumber = `${prefix}${String(nextSequence).padStart(4, '0')}`
  return invoiceNumber
}

// Get projects by client for project selection
export async function getProjectsByClient(clientId: string) {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('projects')
    .select('id, name, status')
    .eq('client_id', clientId)
    .in('status', ['planning', 'in_progress', 'review', 'completed'])
    .order('name', { ascending: true })
  
  if (error) {
    throw new Error(`Failed to fetch projects: ${error.message}`)
  }
  
  return data
}

// Search invoices - Comprehensive search across all relevant fields
export async function searchInvoices(query: string) {
  const supabase = createSupabaseClient()

  // Prepare search promises for different field types
  const searchPromises = [
    // Search invoice table fields (invoice_number, notes)
    supabase
      .from('invoices')
      .select(`
        *,
        client:clients(id, name, company),
        project:projects(id, name),
        created_by_user:users_profiles!created_by(id, full_name)
      `)
      .or(`invoice_number.ilike.%${query}%,notes.ilike.%${query}%`),

    // Search by client name
    supabase
      .from('invoices')
      .select(`
        *,
        client:clients(id, name, company),
        project:projects(id, name),
        created_by_user:users_profiles!created_by(id, full_name)
      `)
      .filter('client.name', 'ilike', `%${query}%`),

    // Search by client company
    supabase
      .from('invoices')
      .select(`
        *,
        client:clients(id, name, company),
        project:projects(id, name),
        created_by_user:users_profiles!created_by(id, full_name)
      `)
      .filter('client.company', 'ilike', `%${query}%`),

    // Search by project name
    supabase
      .from('invoices')
      .select(`
        *,
        client:clients(id, name, company),
        project:projects(id, name),
        created_by_user:users_profiles!created_by(id, full_name)
      `)
      .filter('project.name', 'ilike', `%${query}%`)
  ]

  // If query is numeric, also search total_amount
  const numericQuery = parseFloat(query.replace(/[^\d.-]/g, ''))
  if (!isNaN(numericQuery)) {
    searchPromises.push(
      supabase
        .from('invoices')
        .select(`
          *,
          client:clients(id, name, company),
          project:projects(id, name),
          created_by_user:users_profiles!created_by(id, full_name)
        `)
        .filter('total_amount', 'eq', numericQuery)
    )
  }

  try {
    // Execute all search queries in parallel
    const results = await Promise.all(searchPromises)

    // Check for errors in any of the queries
    for (const result of results) {
      if (result.error) {
        throw new Error(`Failed to search invoices: ${result.error.message}`)
      }
    }

    // Merge and deduplicate results
    const allInvoices = results.flatMap(result => result.data || [])
    const uniqueInvoices = allInvoices.filter((invoice, index, self) =>
      index === self.findIndex(i => i.id === invoice.id)
    )

    // Sort by creation date (newest first)
    return uniqueInvoices.sort((a, b) =>
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    )
  } catch (error) {
    throw new Error(`Failed to search invoices: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Get invoices by status
export async function getInvoicesByStatus(status: string) {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('invoices')
    .select(`
      *,
      client:clients(id, name, company),
      project:projects(id, name)
    `)
    .eq('status', status)
    .order('created_at', { ascending: false })
  
  if (error) {
    throw new Error(`Failed to fetch invoices by status: ${error.message}`)
  }
  
  return data
}

// Get invoices by client ID
export async function getInvoicesByClient(clientId: string) {
  const supabase = createSupabaseClient()

  const { data, error } = await supabase
    .from('invoices')
    .select(`
      *,
      client:clients(id, name, company),
      project:projects(id, name),
      created_by_user:users_profiles!created_by(id, full_name)
    `)
    .eq('client_id', clientId)
    .order('created_at', { ascending: false })

  if (error) {
    throw new Error(`Failed to fetch invoices by client: ${error.message}`)
  }

  return data
}

// Get invoices by project ID
export async function getInvoicesByProject(projectId: string) {
  const supabase = createSupabaseClient()

  const { data, error } = await supabase
    .from('invoices')
    .select(`
      *,
      client:clients(id, name, company),
      project:projects(id, name),
      created_by_user:users_profiles!created_by(id, full_name)
    `)
    .eq('project_id', projectId)
    .order('created_at', { ascending: false })

  if (error) {
    throw new Error(`Failed to fetch invoices by project: ${error.message}`)
  }

  return data
}
