/**
 * Search Validation Test
 * 
 * This file contains validation tests for the updated search functionality
 * to ensure that the PostgREST parsing errors are fixed and comprehensive
 * search works across all relevant columns.
 */

import { searchInvoices } from '@/lib/api/invoices-client'
import { searchProjects } from '@/lib/api/projects-client'

// Test cases for invoice search
export const invoiceSearchValidationTests = [
  {
    name: "Invoice Number Search",
    query: "INV",
    description: "Should find invoices by invoice number",
    expectedFields: ["invoice_number"]
  },
  {
    name: "Client Name Search", 
    query: "<PERSON>",
    description: "Should find invoices by client name",
    expectedFields: ["client.name"]
  },
  {
    name: "Client Company Search",
    query: "Tech",
    description: "Should find invoices by client company",
    expectedFields: ["client.company"]
  },
  {
    name: "Project Name Search",
    query: "Website",
    description: "Should find invoices by project name", 
    expectedFields: ["project.name"]
  },
  {
    name: "Notes Search",
    query: "payment",
    description: "Should find invoices by notes content",
    expectedFields: ["notes"]
  },
  {
    name: "Numeric Amount Search",
    query: "1000000",
    description: "Should find invoices by total amount",
    expectedFields: ["total_amount"]
  },
  {
    name: "Mixed Case Search",
    query: "KANDA",
    description: "Should work with case insensitive search",
    expectedFields: ["invoice_number", "client.name", "client.company", "project.name", "notes"]
  }
]

// Test cases for project search
export const projectSearchValidationTests = [
  {
    name: "Project Name Search",
    query: "Website",
    description: "Should find projects by name",
    expectedFields: ["name"]
  },
  {
    name: "Project Description Search",
    query: "development",
    description: "Should find projects by description",
    expectedFields: ["description"]
  },
  {
    name: "Client Name Search",
    query: "Jane",
    description: "Should find projects by client name",
    expectedFields: ["client.name"]
  },
  {
    name: "Client Company Search",
    query: "Solutions",
    description: "Should find projects by client company",
    expectedFields: ["client.company"]
  },
  {
    name: "Budget Search",
    query: "5000000",
    description: "Should find projects by budget amount",
    expectedFields: ["budget"]
  }
]

/**
 * Manual validation function for invoice search
 * Run this in the browser console to test search functionality
 */
export async function validateInvoiceSearch() {
  console.log("🔍 Starting Invoice Search Validation...")
  
  for (const test of invoiceSearchValidationTests) {
    try {
      console.log(`\n📋 Testing: ${test.name}`)
      console.log(`Query: "${test.query}"`)
      console.log(`Description: ${test.description}`)
      
      const results = await searchInvoices(test.query)
      
      console.log(`✅ Success: Found ${results.length} results`)
      
      if (results.length > 0) {
        console.log("Sample result:", {
          id: results[0].id,
          invoice_number: results[0].invoice_number,
          client_name: results[0].client?.name,
          client_company: results[0].client?.company,
          project_name: results[0].project?.name,
          total_amount: results[0].total_amount,
          notes: results[0].notes?.substring(0, 50) + "..."
        })
      }
      
    } catch (error) {
      console.error(`❌ Error in ${test.name}:`, error)
    }
  }
  
  console.log("\n🎉 Invoice Search Validation Complete!")
}

/**
 * Manual validation function for project search
 * Run this in the browser console to test search functionality
 */
export async function validateProjectSearch() {
  console.log("🔍 Starting Project Search Validation...")
  
  for (const test of projectSearchValidationTests) {
    try {
      console.log(`\n📋 Testing: ${test.name}`)
      console.log(`Query: "${test.query}"`)
      console.log(`Description: ${test.description}`)
      
      const results = await searchProjects(test.query)
      
      console.log(`✅ Success: Found ${results.length} results`)
      
      if (results.length > 0) {
        console.log("Sample result:", {
          id: results[0].id,
          name: results[0].name,
          description: results[0].description?.substring(0, 50) + "...",
          client_name: results[0].client?.name,
          client_company: results[0].client?.company,
          budget: results[0].budget
        })
      }
      
    } catch (error) {
      console.error(`❌ Error in ${test.name}:`, error)
    }
  }
  
  console.log("\n🎉 Project Search Validation Complete!")
}

/**
 * Run all validation tests
 */
export async function runAllValidationTests() {
  await validateInvoiceSearch()
  await validateProjectSearch()
}

// Export for browser console testing
if (typeof window !== 'undefined') {
  (window as any).validateInvoiceSearch = validateInvoiceSearch
  (window as any).validateProjectSearch = validateProjectSearch
  (window as any).runAllValidationTests = runAllValidationTests
}

console.log("🧪 Search validation tests loaded")
console.log("To test in browser console, run:")
console.log("- validateInvoiceSearch()")
console.log("- validateProjectSearch()")
console.log("- runAllValidationTests()")
